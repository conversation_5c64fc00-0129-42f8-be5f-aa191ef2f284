"use strict";
/**
 * @license
 * Copyright 2020 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.paperFormats = void 0;
/**
 * @internal
 *
 * @remarks All A series paper format sizes in inches are calculated from centimeters
 * rounded mathematically to four decimal places.
 */
exports.paperFormats = {
    letter: {
        cm: { width: 21.59, height: 27.94 },
        in: { width: 8.5, height: 11 },
    },
    legal: {
        cm: { width: 21.59, height: 35.56 },
        in: { width: 8.5, height: 14 },
    },
    tabloid: {
        cm: { width: 27.94, height: 43.18 },
        in: { width: 11, height: 17 },
    },
    ledger: {
        cm: { width: 43.18, height: 27.94 },
        in: { width: 17, height: 11 },
    },
    a0: {
        cm: { width: 84.1, height: 118.9 },
        in: { width: 33.1102, height: 46.811 },
    },
    a1: {
        cm: { width: 59.4, height: 84.1 },
        in: { width: 23.3858, height: 33.1102 },
    },
    a2: {
        cm: { width: 42, height: 59.4 },
        in: { width: 16.5354, height: 23.3858 },
    },
    a3: {
        cm: { width: 29.7, height: 42 },
        in: { width: 11.6929, height: 16.5354 },
    },
    a4: {
        cm: { width: 21, height: 29.7 },
        in: { width: 8.2677, height: 11.6929 },
    },
    a5: {
        cm: { width: 14.8, height: 21 },
        in: { width: 5.8268, height: 8.2677 },
    },
    a6: {
        cm: { width: 10.5, height: 14.8 },
        in: { width: 4.1339, height: 5.8268 },
    },
};
//# sourceMappingURL=PDFOptions.js.map