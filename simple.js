const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

class MermaidChartGenerator {
  constructor(options = {}) {
    this.headless = options.headless !== false;
    this.timeout = options.timeout || 30000;
    this.retries = options.retries || 3;
    this.outputDir = options.outputDir || './charts';
  }

  async generateChart(mermaidCode, filename = 'chart') {
    let browser;
    let attempt = 0;

    // Ensure output directory exists
    if (!fs.existsSync(this.outputDir)) {
      fs.mkdirSync(this.outputDir, { recursive: true });
    }

    while (attempt < this.retries) {
      try {
        console.log(`Attempt ${attempt + 1}/${this.retries} - Generating chart...`);

        browser = await puppeteer.launch({
          headless: this.headless,
          args: ['--no-sandbox', '--disable-setuid-sandbox']
        });

        const page = await browser.newPage();

        // Set viewport for consistent rendering
        await page.setViewport({ width: 1200, height: 800 });

        // Navigate to Mermaid Live with timeout
        await page.goto('https://mermaid.live/', {
          waitUntil: 'networkidle2',
          timeout: this.timeout
        });

        // Wait for the editor to be ready
        await this.waitForEditor(page);

        // Input the Mermaid code
        await this.inputMermaidCode(page, mermaidCode);

        // Wait for chart to render
        await this.waitForChartRender(page);

        // Take screenshot
        const outputPath = await this.takeScreenshot(page, filename);

        await browser.close();
        console.log(`✅ Chart successfully generated: ${outputPath}`);
        return outputPath;

      } catch (error) {
        console.error(`❌ Attempt ${attempt + 1} failed:`, error.message);

        if (browser) {
          await browser.close().catch(() => {});
        }

        attempt++;

        if (attempt >= this.retries) {
          throw new Error(`Failed to generate chart after ${this.retries} attempts. Last error: ${error.message}`);
        }

        // Wait before retry
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    }
  }

  async waitForEditor(page) {
    console.log('⏳ Waiting for editor to load...');

    // Try multiple possible selectors for the textarea
    const selectors = [
      'textarea',
      '.monaco-editor textarea',
      '[data-testid="code-editor"] textarea',
      '.cm-editor .cm-content'
    ];

    for (const selector of selectors) {
      try {
        await page.waitForSelector(selector, { timeout: 5000 });
        console.log(`✅ Editor found with selector: ${selector}`);
        return selector;
      } catch (error) {
        console.log(`⚠️ Selector ${selector} not found, trying next...`);
      }
    }

    throw new Error('Could not find editor textarea');
  }

  async inputMermaidCode(page, mermaidCode) {
    console.log('📝 Inputting Mermaid code...');

    // Clear existing content and input new code
    await page.evaluate((code) => {
      // Try different methods to find and update the editor
      const textarea = document.querySelector('textarea') ||
                      document.querySelector('.monaco-editor textarea') ||
                      document.querySelector('[data-testid="code-editor"] textarea');

      if (textarea) {
        // Clear and set new value
        textarea.value = '';
        textarea.focus();
        textarea.value = code;

        // Trigger various events to ensure the editor updates
        textarea.dispatchEvent(new Event('input', { bubbles: true }));
        textarea.dispatchEvent(new Event('change', { bubbles: true }));
        textarea.dispatchEvent(new KeyboardEvent('keyup', { bubbles: true }));
      } else {
        throw new Error('Could not find textarea element');
      }
    }, mermaidCode);

    // Additional wait to ensure the code is processed
    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  async waitForChartRender(page) {
    console.log('🎨 Waiting for chart to render...');

    // Wait for any of these possible chart container selectors
    const chartSelectors = [
      '#graph-1',
      '.mermaid',
      '[data-testid="diagram"]',
      'svg',
      '.diagram-container svg'
    ];

    let chartFound = false;
    const maxWaitTime = 15000; // 15 seconds
    const startTime = Date.now();

    while (!chartFound && (Date.now() - startTime) < maxWaitTime) {
      for (const selector of chartSelectors) {
        try {
          const element = await page.$(selector);
          if (element) {
            // Check if the element has actual content
            const boundingBox = await element.boundingBox();
            if (boundingBox && boundingBox.width > 0 && boundingBox.height > 0) {
              console.log(`✅ Chart rendered with selector: ${selector}`);
              chartFound = true;
              break;
            }
          }
        } catch (error) {
          // Continue trying other selectors
        }
      }

      if (!chartFound) {
        await new Promise(resolve => setTimeout(resolve, 500));
      }
    }

    if (!chartFound) {
      throw new Error('Chart did not render within the expected time');
    }

    // Additional wait to ensure chart is fully rendered
    await new Promise(resolve => setTimeout(resolve, 2000));
  }

  async takeScreenshot(page, filename) {
    console.log('📸 Taking screenshot...');

    // Try to find the chart element for a more precise screenshot
    const chartSelectors = ['#graph-1', '.mermaid', 'svg', '[data-testid="diagram"]'];
    let chartElement = null;

    for (const selector of chartSelectors) {
      chartElement = await page.$(selector);
      if (chartElement) {
        const boundingBox = await chartElement.boundingBox();
        if (boundingBox && boundingBox.width > 0 && boundingBox.height > 0) {
          break;
        }
        chartElement = null;
      }
    }

    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const outputPath = path.join(this.outputDir, `${filename}-${timestamp}.png`);

    if (chartElement) {
      // Screenshot just the chart element
      await chartElement.screenshot({
        path: outputPath,
        type: 'png'
      });
    } else {
      // Fallback to full page screenshot
      console.log('⚠️ Chart element not found, taking full page screenshot');
      await page.screenshot({
        path: outputPath,
        type: 'png',
        fullPage: true
      });
    }

    return outputPath;
  }
}

// Export the class for use in other modules
module.exports = { MermaidChartGenerator };

// Usage example - only run if this file is executed directly
if (require.main === module) {
  (async () => {
    const generator = new MermaidChartGenerator({
      headless: true,
      timeout: 30000,
      retries: 3,
      outputDir: './mermaid-charts'
    });

    const mermaidCode = `
graph TD
    A[Start] --> B{Is it sunny?}
    B -- Yes --> C[Go outside]
    B -- No --> D[Stay inside]
    C --> E[Have fun!]
    D --> E
`;

    try {
      const outputPath = await generator.generateChart(mermaidCode, 'weather-decision');
      console.log(`🎉 Success! Chart saved to: ${outputPath}`);
    } catch (error) {
      console.error('💥 Failed to generate chart:', error.message);
      process.exit(1);
    }
  })();
}
