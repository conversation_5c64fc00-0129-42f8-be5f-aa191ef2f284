# Mermaid Chart Generator

An automated tool for generating and saving Mermaid charts as PNG images using Puppeteer.

## Features

✅ **Robust Error Handling** - Retry logic and comprehensive error messages  
✅ **Multiple Selector Support** - Works with different versions of Mermaid Live  
✅ **Dynamic Waiting** - Intelligent waiting for chart rendering instead of fixed timeouts  
✅ **Flexible Output** - Configurable output directory and file naming  
✅ **Multiple Chart Types** - Supports all Mermaid diagram types  
✅ **Timestamped Files** - Automatic timestamp addition to prevent overwrites  

## Installation

1. Make sure you have Node.js installed
2. Install dependencies:
```bash
npm install
```

## Usage

### Basic Usage

```javascript
const { MermaidChartGenerator } = require('./simple.js');

const generator = new MermaidChartGenerator();

const mermaidCode = `
graph TD
    A[Start] --> B{Decision}
    B -->|Yes| C[Success]
    B -->|No| D[Retry]
`;

const outputPath = await generator.generateChart(mermaidCode, 'my-chart');
```

### Advanced Configuration

```javascript
const generator = new MermaidChartGenerator({
  headless: true,        // Run browser in headless mode
  timeout: 30000,        // Page load timeout (ms)
  retries: 3,           // Number of retry attempts
  outputDir: './charts' // Output directory for images
});
```

### Running the Examples

```bash
# Run the basic example
node simple.js

# Run tests with multiple chart types
node test-charts.js
```

## Supported Chart Types

- Flowcharts
- Sequence Diagrams
- Class Diagrams
- State Diagrams
- Entity Relationship Diagrams
- User Journey
- Gantt Charts
- Pie Charts
- Git Graphs
- And more!

## Configuration Options

| Option | Type | Default | Description |
|--------|------|---------|-------------|
| `headless` | boolean | `true` | Run browser in headless mode |
| `timeout` | number | `30000` | Page load timeout in milliseconds |
| `retries` | number | `3` | Number of retry attempts on failure |
| `outputDir` | string | `'./charts'` | Directory to save generated images |

## Error Handling

The tool includes comprehensive error handling:

- **Network Issues**: Automatic retries with exponential backoff
- **Selector Changes**: Multiple fallback selectors for different site versions
- **Rendering Failures**: Validation that charts actually rendered before screenshot
- **File System**: Automatic directory creation and conflict resolution

## Troubleshooting

### Common Issues

1. **"Chart element not found"**
   - The tool tries multiple selectors automatically
   - If persistent, the Mermaid Live site may have changed

2. **"Failed after X attempts"**
   - Check your internet connection
   - Try increasing the timeout value
   - Verify the Mermaid code syntax is correct

3. **Empty or partial screenshots**
   - Increase the wait time in `waitForChartRender()`
   - Check if the chart is too large for the viewport

### Debug Mode

Set `headless: false` to see the browser in action:

```javascript
const generator = new MermaidChartGenerator({ headless: false });
```

## Output

Generated charts are saved as PNG files with timestamps:
- Format: `{filename}-{timestamp}.png`
- Example: `flowchart-2024-01-15T10-30-45-123Z.png`

## License

MIT License
