const { MermaidChartGenerator } = require('./simple.js');

// Export the class for use in other files
module.exports = { MermaidChartGenerator };

// Test different chart types
(async () => {
  const generator = new MermaidChartGenerator({
    headless: false, // Set to true for production
    timeout: 30000,
    retries: 2,
    outputDir: './test-charts'
  });

  const charts = [
    {
      name: 'flowchart',
      code: `
flowchart TD
    A[Start] --> B{Decision}
    B -->|Yes| C[Action 1]
    B -->|No| D[Action 2]
    C --> E[End]
    D --> E
`
    },
    {
      name: 'sequence-diagram',
      code: `
sequenceDiagram
    participant A as Alice
    participant B as Bob
    A->>B: Hello <PERSON>, how are you?
    B-->>A: Great!
    A-)B: See you later!
`
    },
    {
      name: 'class-diagram',
      code: `
classDiagram
    class Animal {
        +String name
        +int age
        +makeSound()
    }
    class Dog {
        +String breed
        +bark()
    }
    Animal <|-- Dog
`
    },
    {
      name: 'gantt-chart',
      code: `
gantt
    title Project Timeline
    dateFormat  YYYY-MM-DD
    section Planning
    Research    :done, research, 2024-01-01, 2024-01-15
    Design      :active, design, 2024-01-10, 2024-01-25
    section Development
    Frontend    :frontend, 2024-01-20, 2024-02-15
    Backend     :backend, 2024-01-25, 2024-02-20
`
    }
  ];

  console.log('🚀 Starting chart generation tests...\n');

  for (const chart of charts) {
    try {
      console.log(`📊 Generating ${chart.name}...`);
      const outputPath = await generator.generateChart(chart.code, chart.name);
      console.log(`✅ ${chart.name} completed: ${outputPath}\n`);
    } catch (error) {
      console.error(`❌ Failed to generate ${chart.name}:`, error.message, '\n');
    }
  }

  console.log('🎉 All tests completed!');
})();
